import React from "react";
import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    BuildingLibraryIcon,
    GlobeEuropeAfricaIcon,
    HeartIcon,
    LightBulbIcon,
    UsersIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";

export default function AboutPage() {
    return (
        <Layout>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            À propos de PillarScan
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Redonner le pouvoir au peuple français
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            PillarScan transforme la culture française de la plainte en culture de
                            l'action collective, une expression citoyenne à la fois.
                        </p>
                        <div className="flex items-center justify-center mb-6">
                            <BuildingLibraryIcon className="h-8 w-8 mr-3" />
                            <span className="text-lg font-semibold">République Française</span>
                        </div>
                    </div>
                </div>
            </section>

            {/* Mission Section */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <Badge variant="primary" className="mb-4">
                                Notre Mission
                            </Badge>
                            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                                Transformer chaque frustration en opportunité d'amélioration
                            </h2>
                            <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                                Nous croyons que chaque citoyen français mérite d'avoir une voix qui
                                compte vraiment. PillarScan révolutionne la démocratie participative en
                                créant un pont direct entre vos préoccupations quotidiennes et les
                                acteurs capables de les résoudre.
                            </p>
                            <div className="space-y-4">
                                <div className="flex items-start">
                                    <LightBulbIcon className="h-6 w-6 text-primary mr-3 mt-1 flex-shrink-0" />
                                    <div>
                                        <h3 className="font-semibold text-foreground">Innovation démocratique</h3>
                                        <p className="text-muted-foreground">
                                            Premier système démocratique temps réel au monde
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <UsersIcon className="h-6 w-6 text-primary mr-3 mt-1 flex-shrink-0" />
                                    <div>
                                        <h3 className="font-semibold text-foreground">Inclusivité totale</h3>
                                        <p className="text-muted-foreground">
                                            Accessible à tous les citoyens, gratuit et sans barrière
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <ChartBarIcon className="h-6 w-6 text-primary mr-3 mt-1 flex-shrink-0" />
                                    <div>
                                        <h3 className="font-semibold text-foreground">Impact mesurable</h3>
                                        <p className="text-muted-foreground">
                                            Chaque action est trackée et son impact quantifié
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="space-y-6">
                            <Card variant="elevated" className="p-6">
                                <div className="text-center">
                                    <div className="text-4xl font-bold text-primary mb-2">67M</div>
                                    <div className="text-muted-foreground">Citoyens français concernés</div>
                                </div>
                            </Card>
                            <Card variant="elevated" className="p-6">
                                <div className="text-center">
                                    <div className="text-4xl font-bold text-primary mb-2">12</div>
                                    <div className="text-muted-foreground">Piliers de la société couverts</div>
                                </div>
                            </Card>
                            <Card variant="elevated" className="p-6">
                                <div className="text-center">
                                    <div className="text-4xl font-bold text-primary mb-2">35K</div>
                                    <div className="text-muted-foreground">Communes françaises</div>
                                </div>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>

            {/* Vision Section */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="info" size="lg" className="mb-4">
                            Notre Vision
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            La France, laboratoire démocratique du 21ème siècle
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Faire de la France le premier pays au monde où chaque citoyen a un impact
                            direct et mesurable sur l'amélioration de sa société.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card hover className="p-6 text-center">
                            <div className="text-2xl font-bold text-primary mb-2">2024</div>
                            <div className="text-lg font-semibold mb-2">🇫🇷 France</div>
                            <p className="text-sm text-muted-foreground">
                                Déploiement national et preuve du concept
                            </p>
                        </Card>
                        <Card hover className="p-6 text-center">
                            <div className="text-2xl font-bold text-primary mb-2">2025</div>
                            <div className="text-lg font-semibold mb-2">🇪🇺 Europe</div>
                            <p className="text-sm text-muted-foreground">
                                Expansion dans l'Union Européenne
                            </p>
                        </Card>
                        <Card hover className="p-6 text-center">
                            <div className="text-2xl font-bold text-primary mb-2">2027</div>
                            <div className="text-lg font-semibold mb-2">🌍 Afrique</div>
                            <p className="text-sm text-muted-foreground">
                                Adaptation pour l'Afrique francophone
                            </p>
                        </Card>
                        <Card hover className="p-6 text-center">
                            <div className="text-2xl font-bold text-primary mb-2">2030</div>
                            <div className="text-lg font-semibold mb-2">🌏 Monde</div>
                            <p className="text-sm text-muted-foreground">
                                Standard démocratique universel
                            </p>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Values Section */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="success" size="lg" className="mb-4">
                            Nos Valeurs
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Ce qui nous guide chaque jour
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <Card hover className="p-8 text-center">
                            <HeartIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold mb-4 text-foreground">Transparence</h3>
                            <p className="text-muted-foreground">
                                Toutes nos données sont ouvertes, tous nos processus sont publics.
                                La démocratie ne peut exister sans transparence totale.
                            </p>
                        </Card>
                        <Card hover className="p-8 text-center">
                            <UsersIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold mb-4 text-foreground">Égalité</h3>
                            <p className="text-muted-foreground">
                                Chaque voix compte autant, qu'elle vienne de Paris ou d'un village
                                rural. L'égalité citoyenne est notre fondement.
                            </p>
                        </Card>
                        <Card hover className="p-8 text-center">
                            <GlobeEuropeAfricaIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold mb-4 text-foreground">Impact</h3>
                            <p className="text-muted-foreground">
                                Nous ne nous contentons pas de collecter des opinions. Nous créons
                                du changement réel et mesurable.
                            </p>
                        </Card>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Rejoignez le mouvement démocratique
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                        Ensemble, transformons la France en exemple démocratique pour le monde entier.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/auth/register" as={Link}>
                            Rejoindre PillarScan
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            href="/how-it-works"
                            as={Link}
                            className="border-white text-white hover:bg-white hover:text-primary"
                        >
                            Comment ça marche
                        </Button>
                    </div>
                </div>
            </section>
        </Layout>
    );
}
